import type { Metadata } from "next";
import "./globals.css";
import '../components/GooeyNav'
import GooeyNav from "../components/GooeyNav";

export const metadata: Metadata = {
  title: "Sample Cafe Website",
  description: "Created by p<PERSON><PERSON><PERSON>",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
     
      <body suppressHydrationWarning>
         <GooeyNav items={[
        { label: "Home", href: "/" },
        { label: "Menu", href: "/menu" },
        { label: "About", href: "/about" },
        { label: "Contact", href: "/contact" }
      ]} />
        {children}
      </body>
    </html>
  );
}
